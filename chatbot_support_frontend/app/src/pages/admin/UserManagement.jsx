import React, { useState, useEffect } from 'react';
import {
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  UserPlusIcon,
  UserGroupIcon,
  DocumentPlusIcon,
  DocumentTextIcon,
  DocumentArrowUpIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { useUser } from '../../contexts/UserContext';
import { useDocument } from '../../contexts/DocumentContext';
import { useTheme } from '../../contexts/ThemeContext';
import ImprovedDropdown from '../../components/common/ImprovedDropdown';
import { dropdownOptionsAPI } from '../../services/api';

const UserManagement = () => {
  const [activeTab, setActiveTab] = useState('users');
  const [showUserModal, setShowUserModal] = useState(false);
  const [showGroupModal, setShowGroupModal] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedGroup, setSelectedGroup] = useState(null);
  // We only need selectedUser and selectedGroup for editing
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  // Pagination state for documents
  const [currentDocPage, setCurrentDocPage] = useState(1);
  const documentsPerPage = 5; // Show 5 documents per page

  // Get dark mode from context or props
  const darkMode = document.documentElement.classList.contains('dark');

  // Get users and groups from context
  const {
    users: apiUsers,
    groups: apiGroups,
    loading: userLoading,
    error: userError,
    fetchUsers,
    fetchGroups,
    createUser,
    updateUser,
    deleteUser,
    createGroup,
    updateGroup,
    deleteGroup
  } = useUser();

  // Get documents from context
  const {
    documents: apiDocuments,
    fetchDocuments,
    uploadDocument,
    deleteDocument,
    getDocumentUrl
  } = useDocument();

  // Log state for debugging
  console.log('API Users:', apiUsers);
  console.log('API Groups:', apiGroups);
  console.log('API Documents:', apiDocuments);

  // Fetch users and groups on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('Fetching users and groups...');
        setLoading(true);

        // Fetch data sequentially to better handle errors
        try {
          await fetchUsers();
        } catch (userError) {
          console.error('Error fetching users:', userError);
          // Continue with other data fetching even if users fail
        }

        try {
          await fetchGroups();
        } catch (groupError) {
          console.error('Error fetching groups:', groupError);
        }

        try {
          await fetchDocuments();
          // Reset to first page when documents are refreshed
          setCurrentDocPage(1);
        } catch (docError) {
          console.error('Error fetching documents:', docError);
        }

        console.log('Data fetching completed');
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Set up periodic refresh for data
    const refreshInterval = setInterval(() => {
      fetchGroups().catch(err => console.error('Error refreshing groups:', err));
      fetchDocuments()
        .then(() => {
          // Reset to first page when documents are refreshed
          if (activeTab === 'documents') {
            setCurrentDocPage(1);
          }
        })
        .catch(err => console.error('Error refreshing documents:', err));
      // Try to fetch users again in case the API becomes available
      fetchUsers().catch(err => console.error('Error refreshing users:', err));
    }, 60000); // Refresh every minute

    return () => clearInterval(refreshInterval);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Document state is already fetched in the first useEffect

  // Handler function for document upload
  const handleUploadDocument = async (formData) => {
    try {
      // Upload the document
      const result = await uploadDocument(formData);

      if (result.success) {
        // Refresh documents after upload
        await fetchDocuments();
      }

      return result;
    } catch (error) {
      console.error('Error uploading document:', error);

      // Check for specific Google API error
      if (error.message && error.message.includes('Google API key or service account file must be provided')) {
        return {
          success: false,
          error: 'Backend configuration error: Google API credentials are missing. Please contact your administrator to configure Google API credentials.'
        };
      }

      return {
        success: false,
        error: typeof error === 'string' ? error : 'Failed to upload document'
      };
    }
  };

  // Transform users data if needed
  const transformedUsers = apiUsers?.map(user => ({
    id: user.id,
    username: user.username,
    name: user.username, // Use username as name if not provided
    email: user.email || '',
    role: user.role || 'user',
    status: 'Active',
    last_login: user.last_login,
    groups: user.groups || []
  })) || [];

  // Transform groups data if needed
  const transformedGroups = apiGroups?.map(group => ({
    id: group.id,
    name: group.name,
    description: group.description || '',
    members: group.members || [],
    memberCount: group.members?.length || 0
  })) || [];

  // Transform documents data if needed
  const transformedDocuments = apiDocuments || [];

  // Filter users based on search term
  const filteredUsers = transformedUsers.filter(user =>
    (user.username || '')?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.email || '')?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (user.role || '')?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter groups based on search term
  const filteredGroups = transformedGroups.filter(group =>
    (group.name || '')?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description || '')?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter documents based on search term
  const filteredDocuments = transformedDocuments.filter(doc =>
    (doc.name || doc.filename || '')?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (doc.type || doc.content_type || '')?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (doc.status || '')?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate total pages for documents pagination
  const totalDocPages = Math.ceil(filteredDocuments.length / documentsPerPage);

  // Get current page documents
  const indexOfLastDoc = currentDocPage * documentsPerPage;
  const indexOfFirstDoc = indexOfLastDoc - documentsPerPage;
  const currentPageDocuments = filteredDocuments.slice(indexOfFirstDoc, indexOfLastDoc);

  // Handle page changes
  const handlePreviousDocPage = () => {
    setCurrentDocPage(prev => Math.max(prev - 1, 1));
  };

  const handleNextDocPage = () => {
    setCurrentDocPage(prev => Math.min(prev + 1, totalDocPages));
  };

  // User Modal Component
  const UserModal = ({ isOpen, onClose, editUser = null }) => {
    const [formData, setFormData] = useState({
      username: '',
      password: '',
      role: 'user',
      groups: []
    });
    const [formError, setFormError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Reset form when modal opens/closes or when editUser changes
    useEffect(() => {
      if (isOpen) {
        if (editUser) {
          // If editing an existing user, populate the form
          setFormData({
            username: editUser.username || '',
            password: '', // Don't populate password for security
            role: editUser.role || 'user',
            groups: editUser.groups || []
          });
        } else {
          // If creating a new user, reset the form
          setFormData({
            username: '',
            password: '',
            role: 'user',
            groups: []
          });
        }
        setFormError('');
      }
    }, [isOpen, editUser]);

    // Handle input changes
    const handleChange = (e) => {
      const { name, value } = e.target;
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    };

    // Handle group selection
    const handleGroupChange = (e) => {
      const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
      setFormData(prev => ({
        ...prev,
        groups: selectedOptions
      }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
      e.preventDefault();
      setFormError('');

      // Validate form
      if (!formData.username) {
        setFormError('Username is required');
        return;
      }

      if (!editUser && !formData.password) {
        setFormError('Password is required for new users');
        return;
      }

      try {
        setIsSubmitting(true);

        // Prepare user data
        const userData = {
          username: formData.username,
          role: formData.role.toLowerCase(),
          groups: formData.groups
        };

        // Only include password if it's provided (for new users or password changes)
        if (formData.password) {
          userData.password = formData.password;
        }



        let result;

        if (editUser) {
          // Update existing user
          result = await updateUser(editUser.id, userData);
        } else {
          // Create new user
          result = await createUser(userData);
        }

        if (result.success) {
          onClose();
        } else {
          setFormError(result.error || 'Failed to save user');
        }
      } catch (error) {
        console.error('Error saving user:', error);
        setFormError(typeof error === 'string' ? error : 'An unexpected error occurred');
      } finally {
        setIsSubmitting(false);
      }
    };

    if (!isOpen) return null;

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 50,
        overflowY: 'auto'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: '1rem'
        }}>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            transition: 'opacity 0.3s ease-in-out'
          }} aria-hidden="true" onClick={onClose}></div>

          <div style={{
            position: 'relative',
            backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            width: '100%',
            maxWidth: '32rem',
            margin: '2rem auto',
            textAlign: 'left'
          }}>
            <div style={{ padding: '1.5rem' }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '500',
                marginBottom: '1rem',
                color: darkMode ? '#F9FAFB' : '#111827'
              }}>{editUser ? 'Edit User' : 'Add New User'}</h3>

              {formError && (
                <div style={{
                  padding: '0.75rem',
                  marginBottom: '1rem',
                  backgroundColor: darkMode ? '#DC262630' : '#FEE2E2',
                  color: darkMode ? '#FCA5A5' : '#DC2626',
                  borderRadius: '0.375rem'
                }}>
                  {formError}
                </div>
              )}

              <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div>
                  <label htmlFor="username" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Username
                  </label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none'
                    }}
                  />
                </div>



                <div>
                  <label htmlFor="password" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    {editUser ? 'Password (Leave blank to keep current)' : 'Password'}
                  </label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none'
                    }}
                  />
                </div>

                <div>
                  <label htmlFor="role" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Role
                  </label>
                  <select
                    id="role"
                    name="role"
                    value={formData.role}
                    onChange={handleChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none'
                    }}
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="groups" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Groups
                  </label>
                  <select
                    id="groups"
                    name="groups"
                    multiple
                    value={formData.groups}
                    onChange={handleGroupChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none',
                      height: '8rem'
                    }}
                  >
                    {filteredGroups.map(group => (
                      <option key={group.id} value={group.id}>{group.name}</option>
                    ))}
                  </select>
                </div>

                <div style={{
                  padding: '1rem 0',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '0.75rem',
                  marginTop: '1rem'
                }}>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    style={{
                      display: 'inline-flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      backgroundColor: isSubmitting ? (darkMode ? '#4B5563' : '#9CA3AF') : (darkMode ? '#2563EB' : '#3B82F6'),
                      color: 'white',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      border: 'none'
                    }}
                  >
                    {isSubmitting ? 'Saving...' : 'Save'}
                  </button>
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={isSubmitting}
                    style={{
                      display: 'inline-flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      color: darkMode ? '#D1D5DB' : '#374151',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Document Modal Component
  const DocumentModal = ({ isOpen, onClose }) => {
    const { darkMode } = useTheme();
    const [documentName, setDocumentName] = useState('');
    const [documentFile, setDocumentFile] = useState(null);
    const [selectedGroups, setSelectedGroups] = useState([]);
    const [serviceName, setServiceName] = useState('');
    const [softwareMenus, setSoftwareMenus] = useState('');
    const [issueType, setIssueType] = useState('');
    const [uploadError, setUploadError] = useState('');

    // State for dropdown options
    const [serviceNameOptions, setServiceNameOptions] = useState([]);
    const [softwareMenuOptions, setSoftwareMenuOptions] = useState([]);
    const [issueTypeOptions, setIssueTypeOptions] = useState([]);
    const [loadingOptions, setLoadingOptions] = useState(false);

    const fileInputRef = React.useRef(null);

    // Load cached dropdown options on component mount
    useEffect(() => {
      // Try to load cached options from localStorage
      try {
        // Load service name options
        const cachedServiceNames = localStorage.getItem('serviceNameOptions');
        if (cachedServiceNames) {
          const parsedServiceNames = JSON.parse(cachedServiceNames);
          setServiceNameOptions(parsedServiceNames.map(option => ({
            value: option.value,
            label: option.value
          })));
          console.log('Loaded cached service name options on mount');
        }

        // Load software menu options
        const cachedSoftwareMenus = localStorage.getItem('softwareMenuOptions');
        if (cachedSoftwareMenus) {
          const parsedSoftwareMenus = JSON.parse(cachedSoftwareMenus);
          setSoftwareMenuOptions(parsedSoftwareMenus.map(option => ({
            value: option.value,
            label: option.value
          })));
          console.log('Loaded cached software menu options on mount');
        }

        // Load issue type options
        const cachedIssueTypes = localStorage.getItem('issueTypeOptions');
        if (cachedIssueTypes) {
          const parsedIssueTypes = JSON.parse(cachedIssueTypes);
          setIssueTypeOptions(parsedIssueTypes.map(option => ({
            value: option.value,
            label: option.value
          })));
          console.log('Loaded cached issue type options on mount');
        }
      } catch (error) {
        console.error('Error loading cached dropdown options:', error);
      }
    }, []);

    // Fetch dropdown options when modal opens
    useEffect(() => {
      if (isOpen) {
        fetchDropdownOptions();
      }
    }, [isOpen]);

    // Function to fetch dropdown options
    const fetchDropdownOptions = async () => {
      setLoadingOptions(true);
      try {
        // Fetch service name options
        const serviceNames = await dropdownOptionsAPI.getServiceNameOptions();
        const formattedServiceNames = serviceNames.map(option => ({
          value: option.value,
          label: option.value
        }));
        setServiceNameOptions(formattedServiceNames);

        // Store in localStorage for caching
        try {
          localStorage.setItem('serviceNameOptions', JSON.stringify(serviceNames));
        } catch (storageError) {
          console.error('Error storing service name options in localStorage:', storageError);
        }

        // Fetch software menu options
        const softwareMenus = await dropdownOptionsAPI.getSoftwareMenuOptions();
        const formattedSoftwareMenus = softwareMenus.map(option => ({
          value: option.value,
          label: option.value
        }));
        setSoftwareMenuOptions(formattedSoftwareMenus);

        // Store in localStorage for caching
        try {
          localStorage.setItem('softwareMenuOptions', JSON.stringify(softwareMenus));
        } catch (storageError) {
          console.error('Error storing software menu options in localStorage:', storageError);
        }

        // Fetch issue type options
        const issueTypes = await dropdownOptionsAPI.getIssueTypeOptions();
        const formattedIssueTypes = issueTypes.map(option => ({
          value: option.value,
          label: option.value
        }));
        setIssueTypeOptions(formattedIssueTypes);

        // Store in localStorage for caching
        try {
          localStorage.setItem('issueTypeOptions', JSON.stringify(issueTypes));
        } catch (storageError) {
          console.error('Error storing issue type options in localStorage:', storageError);
        }

        console.log('Successfully fetched all dropdown options');
      } catch (error) {
        console.error('Error fetching dropdown options:', error);

        // Try to get options from localStorage first
        let useDefaults = true;

        try {
          // Try to get service name options from localStorage
          const cachedServiceNames = localStorage.getItem('serviceNameOptions');
          if (cachedServiceNames) {
            const parsedServiceNames = JSON.parse(cachedServiceNames);
            setServiceNameOptions(parsedServiceNames.map(option => ({
              value: option.value,
              label: option.value
            })));
            useDefaults = false;
          }

          // Try to get software menu options from localStorage
          const cachedSoftwareMenus = localStorage.getItem('softwareMenuOptions');
          if (cachedSoftwareMenus) {
            const parsedSoftwareMenus = JSON.parse(cachedSoftwareMenus);
            setSoftwareMenuOptions(parsedSoftwareMenus.map(option => ({
              value: option.value,
              label: option.value
            })));
            useDefaults = false;
          }

          // Try to get issue type options from localStorage
          const cachedIssueTypes = localStorage.getItem('issueTypeOptions');
          if (cachedIssueTypes) {
            const parsedIssueTypes = JSON.parse(cachedIssueTypes);
            setIssueTypeOptions(parsedIssueTypes.map(option => ({
              value: option.value,
              label: option.value
            })));
            useDefaults = false;
          }
        } catch (cacheError) {
          console.error('Error reading cached options:', cacheError);
          useDefaults = true;
        }

        // Set default options if API fails and no cache is available
        if (useDefaults) {
          console.log('Using default dropdown options');

          const defaultServiceNames = [
            { value: 'Customer Support', label: 'Customer Support' },
            { value: 'Technical Support', label: 'Technical Support' },
            { value: 'Sales', label: 'Sales' },
            { value: 'Billing', label: 'Billing' },
            { value: 'Other', label: 'Other' }
          ];
          setServiceNameOptions(defaultServiceNames);

          const defaultSoftwareMenus = [
            { value: 'Dashboard', label: 'Dashboard' },
            { value: 'Reports', label: 'Reports' },
            { value: 'Settings', label: 'Settings' },
            { value: 'User Management', label: 'User Management' },
            { value: 'Other', label: 'Other' }
          ];
          setSoftwareMenuOptions(defaultSoftwareMenus);

          const defaultIssueTypes = [
            { value: 'Bug', label: 'Bug' },
            { value: 'Feature Request', label: 'Feature Request' },
            { value: 'Question', label: 'Question' },
            { value: 'Documentation', label: 'Documentation' },
            { value: 'Other', label: 'Other' }
          ];
          setIssueTypeOptions(defaultIssueTypes);
        }
      } finally {
        setLoadingOptions(false);
      }
    };

    // Functions to add new options
    const handleAddServiceName = async (value) => {
      try {
        console.log('Adding new service name option:', value);

        // Create the new option in the database
        const newOption = await dropdownOptionsAPI.createServiceNameOption(value);
        console.log('Successfully added service name option:', newOption);

        // Update the local state with the new option
        const updatedOptions = [...serviceNameOptions, { value: newOption.value, label: newOption.value }];
        setServiceNameOptions(updatedOptions);

        // Refresh all dropdown options to ensure we have the latest data
        fetchDropdownOptions();

        // Return the new option so the dropdown can update
        return newOption;
      } catch (error) {
        console.error('Error adding service name option:', error);

        // Create a mock option to prevent UI errors
        const mockOption = { id: Date.now().toString(), value: value };

        // Update the local state with the mock option
        const updatedOptions = [...serviceNameOptions, { value: mockOption.value, label: mockOption.value }];
        setServiceNameOptions(updatedOptions);

        return mockOption;
      }
    };

    const handleAddSoftwareMenu = async (value) => {
      try {
        console.log('Adding new software menu option:', value);

        // Create the new option in the database
        const newOption = await dropdownOptionsAPI.createSoftwareMenuOption(value);
        console.log('Successfully added software menu option:', newOption);

        // Update the local state with the new option
        const updatedOptions = [...softwareMenuOptions, { value: newOption.value, label: newOption.value }];
        setSoftwareMenuOptions(updatedOptions);

        // Refresh all dropdown options to ensure we have the latest data
        fetchDropdownOptions();

        // Return the new option so the dropdown can update
        return newOption;
      } catch (error) {
        console.error('Error adding software menu option:', error);

        // Create a mock option to prevent UI errors
        const mockOption = { id: Date.now().toString(), value: value };

        // Update the local state with the mock option
        const updatedOptions = [...softwareMenuOptions, { value: mockOption.value, label: mockOption.value }];
        setSoftwareMenuOptions(updatedOptions);

        return mockOption;
      }
    };

    const handleAddIssueType = async (value) => {
      try {
        console.log('Adding new issue type option:', value);

        // Create the new option in the database
        const newOption = await dropdownOptionsAPI.createIssueTypeOption(value);
        console.log('Successfully added issue type option:', newOption);

        // Update the local state with the new option
        const updatedOptions = [...issueTypeOptions, { value: newOption.value, label: newOption.value }];
        setIssueTypeOptions(updatedOptions);

        // Refresh all dropdown options to ensure we have the latest data
        fetchDropdownOptions();

        // Return the new option so the dropdown can update
        return newOption;
      } catch (error) {
        console.error('Error adding issue type option:', error);

        // Create a mock option to prevent UI errors
        const mockOption = { id: Date.now().toString(), value: value };

        // Update the local state with the mock option
        const updatedOptions = [...issueTypeOptions, { value: mockOption.value, label: mockOption.value }];
        setIssueTypeOptions(updatedOptions);

        return mockOption;
      }
    };

    // Reset form on close
    useEffect(() => {
      if (!isOpen) {
        setDocumentName('');
        setDocumentFile(null);
        setSelectedGroups([]);
        setServiceName('');
        setSoftwareMenus('');
        setIssueType('');
        setUploadError('');
      }
    }, [isOpen]);

    // Handle file selection
    const handleFileChange = (e) => {
      const file = e.target.files[0];
      if (file) {
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setUploadError('File size exceeds 10MB limit');
          return;
        }

        // Check file type
        const allowedTypes = [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'text/csv',
          'text/plain'
        ];

        if (!allowedTypes.includes(file.type)) {
          setUploadError('File type not supported. Please upload PDF, DOCX, XLSX, CSV or TXT files.');
          return;
        }

        setDocumentFile(file);
        setUploadError('');

        if (!documentName) {
          setDocumentName(file.name);
        }
      }
    };

    // Handle drag and drop
    const handleDragOver = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDragEnter = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDragLeave = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };

    const handleDrop = (e) => {
      e.preventDefault();
      e.stopPropagation();

      const file = e.dataTransfer.files[0];
      if (file) {
        // Check file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          setUploadError('File size exceeds 10MB limit');
          return;
        }

        // Check file type
        const allowedTypes = [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'text/csv',
          'text/plain'
        ];

        if (!allowedTypes.includes(file.type)) {
          setUploadError('File type not supported. Please upload PDF, DOCX, XLSX, CSV or TXT files.');
          return;
        }

        setDocumentFile(file);
        setUploadError('');

        if (!documentName) {
          setDocumentName(file.name);
        }
      }
    };

    // Handle group selection
    const handleGroupChange = (e) => {
      const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
      setSelectedGroups(selectedOptions);
    };



    // Handle form submission
    const handleSubmit = async (e) => {
      e.preventDefault();

      if (!documentFile) {
        setUploadError('Please select a file to upload');
        return;
      }

      if (!documentName) {
        setUploadError('Please enter a document name');
        return;
      }

      try {
        // Close the modal immediately
        onClose();

        // Set the main loading state to true (this will show the loading overlay on the main dashboard)
        setLoading(true);

        const formData = new FormData();
        formData.append('file', documentFile);

        // Add the selected groups as a JSON string
        if (selectedGroups.length > 0) {
          // Add as JSON string for the backend to parse
          formData.append('groups', JSON.stringify(selectedGroups));

          // Also add each group ID as a separate parameter for the new backend format
          selectedGroups.forEach(groupId => {
            formData.append('group_ids', groupId);
          });
        }

        // Add the metadata fields
        if (serviceName) {
          formData.append('service_name', serviceName);
        }

        if (softwareMenus) {
          formData.append('software_menus', softwareMenus);
        }

        if (issueType) {
          formData.append('issue_type', issueType);
        }

        // Upload the document
        const result = await handleUploadDocument(formData);

        if (!result.success) {
          // If there's an error, show an alert with a user-friendly message
          let errorMessage = result.error || 'Unknown error';

          // Check for specific error types and provide more user-friendly messages
          if (errorMessage.includes('Google API key or service account file must be provided') ||
              errorMessage.includes('Google API credentials are missing') ||
              errorMessage.includes('Google API credentials are not properly configured')) {
            errorMessage = 'The document has been stored in the system, but cannot be processed for AI search because Google API credentials are not properly configured. You can still view and manage the document, but it will not be available for AI-powered search until the administrator configures Google API credentials.';
          }

          alert(`Failed to upload document: ${errorMessage}`);
        }
      } catch (error) {
        console.error('Error uploading document:', error);
        alert(`Error uploading document: ${error.message || 'An unexpected error occurred'}`);
      } finally {
        // Set the main loading state back to false
        setLoading(false);
      }
    };

    if (!isOpen) return null;

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 50,
        overflowY: 'auto'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: '1rem'
        }}>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            transition: 'opacity 0.3s ease-in-out'
          }} aria-hidden="true" onClick={onClose}></div>

          <div style={{
            position: 'relative',
            backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            width: '100%',
            maxWidth: '32rem',
            margin: '2rem auto',
            textAlign: 'left'
          }}>
            <div style={{ padding: '1.5rem' }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '500',
                marginBottom: '1rem',
                color: darkMode ? '#F9FAFB' : '#111827'
              }}>Upload Document</h3>

              {uploadError && (
                <div style={{
                  padding: '0.75rem',
                  marginBottom: '1rem',
                  backgroundColor: darkMode ? '#DC262630' : '#FEE2E2',
                  color: darkMode ? '#FCA5A5' : '#DC2626',
                  borderRadius: '0.375rem'
                }}>
                  {uploadError}
                </div>
              )}

              <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div>
                  <label htmlFor="documentName" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Document Name
                  </label>
                  <input
                    type="text"
                    id="documentName"
                    value={documentName}
                    onChange={(e) => setDocumentName(e.target.value)}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none'
                    }}
                  />
                </div>

                <div>
                  <label htmlFor="documentFile" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Upload File
                  </label>
                  <div
                    onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onDragEnter={handleDragEnter}
                    onDragLeave={handleDragLeave}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '2rem',
                      borderWidth: '2px',
                      borderStyle: 'dashed',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#F9FAFB',
                      cursor: 'pointer',
                      opacity: 1,
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >


                    {documentFile ? (
                      <>
                        <DocumentTextIcon style={{ height: '2.5rem', width: '2.5rem', color: darkMode ? '#60A5FA' : '#2563EB', marginBottom: '0.5rem' }} />
                        <p style={{ fontSize: '0.875rem', color: darkMode ? '#D1D5DB' : '#374151', marginBottom: '0.25rem', fontWeight: '500' }}>
                          {documentFile.name}
                        </p>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginBottom: '0.5rem' }}>
                          <span style={{
                            fontSize: '0.75rem',
                            color: darkMode ? '#9CA3AF' : '#6B7280',
                            backgroundColor: darkMode ? '#4B5563' : '#E5E7EB',
                            padding: '0.125rem 0.375rem',
                            borderRadius: '0.25rem'
                          }}>
                            {documentFile.name.split('.').pop().toUpperCase()}
                          </span>
                          <span style={{ fontSize: '0.75rem', color: darkMode ? '#9CA3AF' : '#6B7280' }}>
                            {(documentFile.size / 1024 / 1024).toFixed(2)} MB
                          </span>
                        </div>
                        <p style={{ fontSize: '0.75rem', color: darkMode ? '#9CA3AF' : '#6B7280' }}>
                          Click to change file
                        </p>
                      </>
                    ) : (
                      <>
                        <DocumentArrowUpIcon style={{ height: '2.5rem', width: '2.5rem', color: darkMode ? '#9CA3AF' : '#6B7280', marginBottom: '0.5rem' }} />
                        <p style={{ fontSize: '0.875rem', color: darkMode ? '#D1D5DB' : '#374151', marginBottom: '0.25rem' }}>
                          Drag and drop your file here, or click to browse
                        </p>
                        <p style={{ fontSize: '0.75rem', color: darkMode ? '#9CA3AF' : '#6B7280' }}>
                          Supports PDF, DOCX, XLSX, CSV, TXT (Max 10MB)
                        </p>
                      </>
                    )}
                    <input
                      type="file"
                      id="documentFile"
                      ref={fileInputRef}
                      onChange={handleFileChange}
                      accept=".pdf,.docx,.xlsx,.csv,.txt"
                      style={{ display: 'none' }}
                      disabled={false}
                    />
                  </div>
                </div>

                {/* Metadata fields */}
                <ImprovedDropdown
                  id="serviceName"
                  label="Service Name"
                  value={serviceName}
                  onChange={setServiceName}
                  options={serviceNameOptions}
                  onAddOption={handleAddServiceName}
                  placeholder="Select a service"
                  addNewText="+ Add new"
                  disabled={loadingOptions}
                />

                <ImprovedDropdown
                  id="softwareMenus"
                  label="Software Menus"
                  value={softwareMenus}
                  onChange={setSoftwareMenus}
                  options={softwareMenuOptions}
                  onAddOption={handleAddSoftwareMenu}
                  placeholder="Select software menu"
                  addNewText="+ Add new"
                  disabled={loadingOptions}
                />

                <ImprovedDropdown
                  id="issueType"
                  label="Issue Type"
                  value={issueType}
                  onChange={setIssueType}
                  options={issueTypeOptions}
                  onAddOption={handleAddIssueType}
                  placeholder="Select issue type"
                  addNewText="+ Add new"
                  disabled={loadingOptions}
                />

                <div>
                  <label htmlFor="documentGroups" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Assign to Groups
                  </label>
                  <select
                    id="documentGroups"
                    multiple
                    value={selectedGroups}
                    onChange={handleGroupChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none',
                      height: '8rem'
                    }}
                  >
                    {filteredGroups.map(group => (
                      <option key={group.id} value={group.id}>{group.name}</option>
                    ))}
                  </select>
                  <p style={{ fontSize: '0.75rem', color: darkMode ? '#9CA3AF' : '#6B7280', marginTop: '0.25rem' }}>
                    Only users in these groups will have access to this document
                  </p>
                </div>

                <div style={{
                  padding: '1rem 0',
                  marginTop: '0.5rem',
                  borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`,
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '0.75rem'
                }}>
                  <button
                    type="submit"
                    style={{
                      display: 'inline-flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#2563EB' : '#3B82F6',
                      color: 'white',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: 'pointer',
                      border: 'none',
                      minWidth: '10rem',
                      transition: 'background-color 0.3s ease'
                    }}
                  >
                    Upload Document
                  </button>
                  <button
                    type="button"
                    onClick={onClose}
                    style={{
                      display: 'inline-flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      color: darkMode ? '#D1D5DB' : '#374151',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: 'pointer',
                      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Group Modal Component
  const GroupModal = ({ isOpen, onClose, editGroup = null }) => {
    const [formData, setFormData] = useState({
      name: '',
      description: '',
      members: []
    });
    const [formError, setFormError] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Reset form when modal opens/closes or when editGroup changes
    useEffect(() => {
      if (isOpen) {
        if (editGroup) {
          // If editing an existing group, populate the form
          setFormData({
            name: editGroup.name || '',
            description: editGroup.description || '',
            members: editGroup.members || []
          });
        } else {
          // If creating a new group, reset the form
          setFormData({
            name: '',
            description: '',
            members: []
          });
        }
        setFormError('');
      }
    }, [isOpen, editGroup]);

    // Handle input changes
    const handleChange = (e) => {
      const { name, value } = e.target;
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    };

    // Handle member selection
    const handleMemberChange = (e) => {
      const selectedOptions = Array.from(e.target.selectedOptions).map(option => option.value);
      setFormData(prev => ({
        ...prev,
        members: selectedOptions
      }));
    };

    // Handle form submission
    const handleSubmit = async (e) => {
      e.preventDefault();
      setFormError('');

      // Validate form
      if (!formData.name) {
        setFormError('Group name is required');
        return;
      }

      try {
        setIsSubmitting(true);

        // Prepare group data
        const groupData = {
          name: formData.name,
          description: formData.description,
          members: formData.members
        };

        let result;

        if (editGroup) {
          // Update existing group
          result = await updateGroup(editGroup.id, groupData);
        } else {
          // Create new group
          result = await createGroup(groupData);
        }

        if (result.success) {
          onClose();
        } else {
          setFormError(result.error || 'Failed to save group');
        }
      } catch (error) {
        console.error('Error saving group:', error);
        setFormError(typeof error === 'string' ? error : 'An unexpected error occurred');
      } finally {
        setIsSubmitting(false);
      }
    };

    if (!isOpen) return null;

    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 50,
        overflowY: 'auto'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          padding: '1rem'
        }}>
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            transition: 'opacity 0.3s ease-in-out'
          }} aria-hidden="true" onClick={onClose}></div>

          <div style={{
            position: 'relative',
            backgroundColor: darkMode ? '#1F2937' : '#FFFFFF',
            borderRadius: '0.5rem',
            overflow: 'hidden',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            width: '100%',
            maxWidth: '32rem',
            margin: '2rem auto',
            textAlign: 'left'
          }}>
            <div style={{ padding: '1.5rem' }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '500',
                marginBottom: '1rem',
                color: darkMode ? '#F9FAFB' : '#111827'
              }}>{editGroup ? 'Edit Group' : 'Add New Group'}</h3>

              {formError && (
                <div style={{
                  padding: '0.75rem',
                  marginBottom: '1rem',
                  backgroundColor: darkMode ? '#DC262630' : '#FEE2E2',
                  color: darkMode ? '#FCA5A5' : '#DC2626',
                  borderRadius: '0.375rem'
                }}>
                  {formError}
                </div>
              )}

              <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                <div>
                  <label htmlFor="name" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Group Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none'
                    }}
                  />
                </div>

                <div>
                  <label htmlFor="description" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows="3"
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none',
                      minHeight: '5rem'
                    }}
                  ></textarea>
                </div>

                <div>
                  <label htmlFor="members" style={{
                    display: 'block',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    marginBottom: '0.5rem',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    Members
                  </label>
                  <select
                    id="members"
                    name="members"
                    multiple
                    value={formData.members}
                    onChange={handleMemberChange}
                    style={{
                      display: 'block',
                      width: '100%',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      borderWidth: '1px',
                      borderStyle: 'solid',
                      borderColor: darkMode ? '#4B5563' : '#D1D5DB',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      outline: 'none',
                      height: '8rem'
                    }}
                  >
                    {filteredUsers.map(user => (
                      <option key={user.id} value={user.id}>{user.name || user.username}</option>
                    ))}
                  </select>
                </div>

                <div style={{
                  padding: '1rem 0',
                  display: 'flex',
                  justifyContent: 'flex-end',
                  gap: '0.75rem',
                  marginTop: '1rem'
                }}>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    style={{
                      display: 'inline-flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      backgroundColor: isSubmitting ? (darkMode ? '#4B5563' : '#9CA3AF') : (darkMode ? '#059669' : '#10B981'),
                      color: 'white',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      border: 'none'
                    }}
                  >
                    {isSubmitting ? 'Saving...' : 'Save'}
                  </button>
                  <button
                    type="button"
                    onClick={onClose}
                    disabled={isSubmitting}
                    style={{
                      display: 'inline-flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '0.5rem 1rem',
                      borderRadius: '0.375rem',
                      backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                      color: darkMode ? '#D1D5DB' : '#374151',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: isSubmitting ? 'not-allowed' : 'pointer',
                      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`
                    }}
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Debug component to show data
  const DebugPanel = () => {
    const [showDebug, setShowDebug] = useState(false);

    if (!showDebug) {
      return (
        <button
          onClick={() => setShowDebug(true)}
          style={{
            position: 'fixed',
            bottom: '10px',
            right: '10px',
            zIndex: 1000,
            padding: '5px 10px',
            backgroundColor: '#333',
            color: 'white',
            border: 'none',
            borderRadius: '4px'
          }}
        >
          Show Debug
        </button>
      );
    }

    return (
      <div style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        zIndex: 1000,
        width: '400px',
        maxHeight: '400px',
        overflowY: 'auto',
        backgroundColor: '#333',
        color: 'white',
        padding: '10px',
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        <button
          onClick={() => setShowDebug(false)}
          style={{
            position: 'absolute',
            top: '5px',
            right: '5px',
            backgroundColor: 'transparent',
            color: 'white',
            border: 'none'
          }}
        >
          X
        </button>
        <h3>Users ({apiUsers?.length || 0})</h3>
        <pre>{JSON.stringify(apiUsers, null, 2)}</pre>
        <h3>Groups ({apiGroups?.length || 0})</h3>
        <pre>{JSON.stringify(apiGroups, null, 2)}</pre>
        <h3>Documents ({apiDocuments?.length || 0})</h3>
        <pre>{JSON.stringify(apiDocuments, null, 2)}</pre>
      </div>
    );
  };

  return (
    <div className="card" style={{ height: '100%', display: 'flex', flexDirection: 'column', position: 'relative', maxWidth: '100%', overflow: 'hidden' }}>
      {/* Loading Overlay */}
      {loading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            width: '5rem',
            height: '5rem',
            borderRadius: '50%',
            border: '0.25rem solid rgba(255, 255, 255, 0.3)',
            borderTopColor: '#fff',
            animation: 'spin 1s linear infinite'
          }}></div>
          <style>
            {`
              @keyframes spin {
                to { transform: rotate(360deg); }
              }
            `}
          </style>
        </div>
      )}
      {/* Debug Panel */}
      <DebugPanel />
      {/* Page header */}
      <div style={{
        marginBottom: '1.5rem',
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap' }}>
          <h1 style={{ fontSize: '1.5rem', fontWeight: '600' }}>User & Group Management</h1>
          <div style={{ display: 'flex', gap: '0.75rem' }}>
            {activeTab === 'users' && (
              <button
                onClick={() => setShowUserModal(true)}
                className="btn btn-primary"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: darkMode ? '#2563EB' : '#3B82F6',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  cursor: 'pointer'
                }}
              >
                <UserPlusIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                Add User
              </button>
            )}

            {activeTab === 'groups' && (
              <button
                onClick={() => setShowGroupModal(true)}
                className="btn btn-primary"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: darkMode ? '#059669' : '#10B981',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  cursor: 'pointer'
                }}
              >
                <UserGroupIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                Add Group
              </button>
            )}

            {activeTab === 'documents' && (
              <button
                onClick={() => setShowDocumentModal(true)}
                className="btn btn-primary"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  backgroundColor: darkMode ? '#7C3AED' : '#8B5CF6',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.375rem',
                  cursor: 'pointer'
                }}
              >
                <DocumentPlusIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                Upload Document
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div style={{ marginBottom: '1.5rem' }}>
        <div style={{
          borderBottom: `1px solid ${darkMode ? '#374151' : '#E5E7EB'}`,
          display: 'flex'
        }}>
          <nav style={{ display: 'flex', gap: '2rem' }}>
            <button
              onClick={() => {
                setActiveTab('users');
                setCurrentDocPage(1); // Reset pagination when switching tabs
              }}
              style={{
                padding: '1rem 0.25rem',
                borderBottom: `2px solid ${activeTab === 'users'
                  ? (darkMode ? '#3B82F6' : '#2563EB')
                  : 'transparent'}`,
                fontWeight: '500',
                fontSize: '0.875rem',
                color: activeTab === 'users'
                  ? (darkMode ? '#3B82F6' : '#2563EB')
                  : (darkMode ? '#9CA3AF' : '#6B7280'),
                cursor: 'pointer',
                background: 'none',
                borderWidth: 0,
                borderStyle: 'none',
                borderRadius: 0,
                marginBottom: '-1px'
              }}
            >
              Users
            </button>
            <button
              onClick={() => {
                setActiveTab('groups');
                setCurrentDocPage(1); // Reset pagination when switching tabs
              }}
              style={{
                padding: '1rem 0.25rem',
                borderBottom: `2px solid ${activeTab === 'groups'
                  ? (darkMode ? '#3B82F6' : '#2563EB')
                  : 'transparent'}`,
                fontWeight: '500',
                fontSize: '0.875rem',
                color: activeTab === 'groups'
                  ? (darkMode ? '#3B82F6' : '#2563EB')
                  : (darkMode ? '#9CA3AF' : '#6B7280'),
                cursor: 'pointer',
                background: 'none',
                borderWidth: 0,
                borderStyle: 'none',
                borderRadius: 0,
                marginBottom: '-1px'
              }}
            >
              Groups
            </button>
            <button
              onClick={() => {
                setActiveTab('documents');
                setCurrentDocPage(1); // Reset pagination when switching tabs
              }}
              style={{
                padding: '1rem 0.25rem',
                borderBottom: `2px solid ${activeTab === 'documents'
                  ? (darkMode ? '#3B82F6' : '#2563EB')
                  : 'transparent'}`,
                fontWeight: '500',
                fontSize: '0.875rem',
                color: activeTab === 'documents'
                  ? (darkMode ? '#3B82F6' : '#2563EB')
                  : (darkMode ? '#9CA3AF' : '#6B7280'),
                cursor: 'pointer',
                background: 'none',
                borderWidth: 0,
                borderStyle: 'none',
                borderRadius: 0,
                marginBottom: '-1px',
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem'
              }}
            >
              <DocumentTextIcon style={{ height: '1rem', width: '1rem' }} />
              Documents
            </button>
          </nav>
        </div>
      </div>

      {/* Search bar */}
      <div style={{ marginBottom: '1.5rem', maxWidth: '100%', overflow: 'hidden' }}>
        <div style={{ position: 'relative', maxWidth: '100%' }}>
          <div style={{
            position: 'absolute',
            top: '50%',
            transform: 'translateY(-50%)',
            left: '0.75rem',
            pointerEvents: 'none'
          }}>
            <MagnifyingGlassIcon style={{
              height: '1.25rem',
              width: '1.25rem',
              color: darkMode ? '#9CA3AF' : '#6B7280'
            }} />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              // Reset pagination when search term changes
              setCurrentDocPage(1);
            }}
            style={{
              display: 'block',
              width: '100%',
              maxWidth: '100%',
              paddingLeft: '2.5rem',
              paddingRight: '0.75rem',
              paddingTop: '0.5rem',
              paddingBottom: '0.5rem',
              borderRadius: '0.375rem',
              backgroundColor: darkMode ? '#374151' : '#FFFFFF',
              borderWidth: '1px',
              borderStyle: 'solid',
              borderColor: darkMode ? '#4B5563' : '#D1D5DB',
              color: darkMode ? '#F9FAFB' : '#111827',
              outline: 'none',
              boxSizing: 'border-box'
            }}
            placeholder={`Search ${activeTab}...`}
          />
        </div>
      </div>

      {/* Content based on active tab */}
      <div style={{
        flex: '1',
        borderRadius: '0.5rem',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        overflow: 'hidden',
        backgroundColor: darkMode ? '#374151' : '#FFFFFF'
      }}>
        {/* Loading and Error States */}
        {userLoading && (
          <div style={{
            padding: '1rem',
            textAlign: 'center'
          }}>
            <p style={{ color: darkMode ? '#D1D5DB' : '#6B7280' }}>Loading data...</p>
          </div>
        )}

        {userError && (
          <div style={{
            padding: '1rem',
            backgroundColor: darkMode ? '#DC262630' : '#FEE2E2',
            color: darkMode ? '#FCA5A5' : '#DC2626',
          }}>
            <p>Error: {userError}</p>
            <button
              onClick={() => {
                fetchUsers();
                fetchGroups();
              }}
              style={{
                marginTop: '0.5rem',
                padding: '0.25rem 0.75rem',
                backgroundColor: darkMode ? '#374151' : '#F3F4F6',
                color: darkMode ? '#D1D5DB' : '#374151',
                borderRadius: '0.25rem',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              Retry
            </button>
          </div>
        )}

        {!userLoading && !userError && activeTab === 'users' ? (
          <div style={{ overflowX: 'auto' }}>
            {filteredUsers.length === 0 ? (
              <div style={{
                padding: '2rem',
                textAlign: 'center',
                color: darkMode ? '#D1D5DB' : '#6B7280'
              }}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '1rem'
                }}>
                  <UserPlusIcon style={{
                    height: '3rem',
                    width: '3rem',
                    color: darkMode ? '#4B5563' : '#D1D5DB'
                  }} />
                  <h3 style={{
                    fontSize: '1.125rem',
                    fontWeight: '500',
                    color: darkMode ? '#F9FAFB' : '#111827',
                    margin: 0
                  }}>
                    No users found
                  </h3>
                  <p style={{
                    fontSize: '0.875rem',
                    maxWidth: '24rem',
                    margin: '0 auto 1rem auto'
                  }}>
                    {searchTerm ?
                      `No users match the search term "${searchTerm}". Try a different search or clear the filter.` :
                      'There are no users in the system yet, or the users API is currently unavailable. You can add a new user using the button above.'}
                  </p>
                  <button
                    onClick={() => {
                      setSelectedUser(null);
                      setShowUserModal(true);
                    }}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '0.5rem 1rem',
                      backgroundColor: darkMode ? '#3B82F6' : '#2563EB',
                      color: 'white',
                      borderRadius: '0.375rem',
                      fontWeight: '500',
                      fontSize: '0.875rem',
                      cursor: 'pointer',
                      border: 'none'
                    }}
                  >
                    <UserPlusIcon style={{ height: '1rem', width: '1rem' }} />
                    Add New User
                  </button>
                </div>
              </div>
            ) : (
              <table style={{
                minWidth: '100%',
                borderCollapse: 'collapse'
              }}>
                <thead style={{
                  backgroundColor: darkMode ? '#4B5563' : '#F9FAFB',
                  borderBottom: `1px solid ${darkMode ? '#6B7280' : '#E5E7EB'}`
                }}>
                  <tr>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Name
                    </th>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Email
                    </th>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Role
                    </th>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Status
                    </th>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Groups
                    </th>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'left',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Last Login
                    </th>
                    <th style={{
                      padding: '0.75rem 1.5rem',
                      textAlign: 'right',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      color: darkMode ? '#D1D5DB' : '#6B7280',
                      textTransform: 'uppercase',
                      letterSpacing: '0.05em'
                    }}>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody style={{
                  backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                  borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
                }}>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} style={{
                      borderBottom: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
                    }}>
                      <td style={{
                        padding: '1rem 1.5rem',
                        whiteSpace: 'nowrap',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        color: darkMode ? '#F9FAFB' : '#111827'
                      }}>
                        {user.name || user.username || 'Unknown'}
                      </td>
                      <td style={{
                        padding: '1rem 1.5rem',
                        whiteSpace: 'nowrap',
                        fontSize: '0.875rem',
                        color: darkMode ? '#D1D5DB' : '#6B7280'
                      }}>
                        {user.email || 'N/A'}
                      </td>
                      <td style={{
                        padding: '1rem 1.5rem',
                        whiteSpace: 'nowrap',
                        fontSize: '0.875rem',
                        color: darkMode ? '#D1D5DB' : '#6B7280'
                      }}>
                        <span style={{
                          padding: '0.125rem 0.5rem',
                          display: 'inline-flex',
                          fontSize: '0.75rem',
                          lineHeight: '1.25rem',
                          fontWeight: '600',
                          borderRadius: '9999px',
                          backgroundColor: (user.role || '').toLowerCase() === 'admin'
                            ? (darkMode ? '#7e22ce30' : '#f3e8ff')
                            : (darkMode ? '#1d4ed830' : '#dbeafe'),
                          color: (user.role || '').toLowerCase() === 'admin'
                            ? (darkMode ? '#e9d5ff' : '#7e22ce')
                            : (darkMode ? '#bfdbfe' : '#1d4ed8')
                        }}>
                          {user.role || 'User'}
                        </span>
                      </td>
                      <td style={{
                        padding: '1rem 1.5rem',
                        whiteSpace: 'nowrap',
                        fontSize: '0.875rem',
                        color: darkMode ? '#D1D5DB' : '#6B7280'
                      }}>
                        <span style={{
                          padding: '0.125rem 0.5rem',
                          display: 'inline-flex',
                          fontSize: '0.75rem',
                          lineHeight: '1.25rem',
                          fontWeight: '600',
                          borderRadius: '9999px',
                          backgroundColor: (user.status || 'Active') === 'Active'
                            ? (darkMode ? '#05966930' : '#d1fae5')
                            : (darkMode ? '#dc262630' : '#fee2e2'),
                          color: (user.status || 'Active') === 'Active'
                            ? (darkMode ? '#a7f3d0' : '#059669')
                            : (darkMode ? '#fca5a5' : '#dc2626')
                        }}>
                          {user.status || 'Active'}
                        </span>
                      </td>
                      <td style={{
                        padding: '1rem 1.5rem',
                        fontSize: '0.875rem',
                        color: darkMode ? '#D1D5DB' : '#6B7280'
                      }}>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>
                          {user.groups && Array.isArray(user.groups) && user.groups.length > 0 ? (
                            user.groups.map((groupId, index) => {
                              // Find the group name from the groups array
                              const group = apiGroups.find(g => g.id === groupId);
                              return (
                                <span key={index} style={{
                                  padding: '0.125rem 0.5rem',
                                  display: 'inline-flex',
                                  fontSize: '0.75rem',
                                  lineHeight: '1.25rem',
                                  fontWeight: '600',
                                  borderRadius: '9999px',
                                  backgroundColor: darkMode ? '#1d4ed830' : '#dbeafe',
                                  color: darkMode ? '#bfdbfe' : '#1d4ed8'
                                }}>
                                  {group ? group.name : groupId}
                                </span>
                              );
                            })
                          ) : (
                            <span style={{
                              padding: '0.125rem 0.5rem',
                              display: 'inline-flex',
                              fontSize: '0.75rem',
                              lineHeight: '1.25rem',
                              fontWeight: '600',
                              borderRadius: '9999px',
                              backgroundColor: darkMode ? '#4b546330' : '#f3f4f6',
                              color: darkMode ? '#9ca3af' : '#6b7280'
                            }}>
                              No groups
                            </span>
                          )}
                        </div>
                      </td>
                      <td style={{
                        padding: '1rem 1.5rem',
                        whiteSpace: 'nowrap',
                        fontSize: '0.875rem',
                        color: darkMode ? '#D1D5DB' : '#6B7280'
                      }}>
                        {user.last_login || 'Never'}
                      </td>
                      <td style={{
                        padding: '1rem 1.5rem',
                        whiteSpace: 'nowrap',
                        textAlign: 'right',
                        fontSize: '0.875rem',
                        fontWeight: '500'
                      }}>
                        <button
                          onClick={() => {
                            setSelectedUser(user);
                            setShowUserModal(true);
                          }}
                          style={{
                            background: 'none',
                            borderWidth: 0,
                            borderStyle: 'none',
                            cursor: 'pointer',
                            color: darkMode ? '#60A5FA' : '#2563EB',
                            marginRight: '0.75rem'
                          }}
                          title="Edit User"
                        >
                          <PencilIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                        </button>
                        <button
                          onClick={async () => {
                            if (window.confirm(`Are you sure you want to delete user "${user.username}"?`)) {
                              try {
                                const result = await deleteUser(user.id);
                                if (!result.success) {
                                  alert(`Failed to delete user: ${result.error || 'Unknown error'}`);
                                }
                              } catch (error) {
                                console.error('Error deleting user:', error);
                                alert(`Error deleting user: ${error.message || error}`);
                              }
                            }
                          }}
                          style={{
                            background: 'none',
                            borderWidth: 0,
                            borderStyle: 'none',
                            cursor: 'pointer',
                            color: darkMode ? '#F87171' : '#DC2626'
                          }}
                          title="Delete User"
                        >
                          <TrashIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        ) : !userLoading && !userError && activeTab === 'groups' ? (
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              minWidth: '100%',
              borderCollapse: 'collapse'
            }}>
              <thead style={{
                backgroundColor: darkMode ? '#4B5563' : '#F9FAFB',
                borderBottom: `1px solid ${darkMode ? '#6B7280' : '#E5E7EB'}`
              }}>
                <tr>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Name
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Description
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Members
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'right',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody style={{
                backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
              }}>
                {filteredGroups.map((group) => (
                  <tr key={group.id} style={{
                    borderBottom: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
                  }}>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: darkMode ? '#F9FAFB' : '#111827'
                    }}>
                      {group.name}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {group.description}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        display: 'inline-flex',
                        fontSize: '0.75rem',
                        lineHeight: '1.25rem',
                        fontWeight: '600',
                        borderRadius: '9999px',
                        backgroundColor: darkMode ? '#1d4ed830' : '#dbeafe',
                        color: darkMode ? '#bfdbfe' : '#1d4ed8'
                      }}>
                        {group.memberCount || 0} users
                      </span>
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      textAlign: 'right',
                      fontSize: '0.875rem',
                      fontWeight: '500'
                    }}>
                      <button
                        onClick={() => {
                          setSelectedGroup(group);
                          setShowGroupModal(true);
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          color: darkMode ? '#60A5FA' : '#2563EB',
                          marginRight: '0.75rem'
                        }}
                        title="Edit Group"
                      >
                        <PencilIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                      </button>
                      <button
                        onClick={async () => {
                          if (window.confirm(`Are you sure you want to delete group "${group.name}"?`)) {
                            try {
                              const result = await deleteGroup(group.id);
                              if (!result.success) {
                                alert(`Failed to delete group: ${result.error || 'Unknown error'}`);
                              }
                            } catch (error) {
                              console.error('Error deleting group:', error);
                              alert(`Error deleting group: ${error.message || error}`);
                            }
                          }
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          cursor: 'pointer',
                          color: darkMode ? '#F87171' : '#DC2626'
                        }}
                        title="Delete Group"
                      >
                        <TrashIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : !userLoading && !userError && activeTab === 'documents' ? (
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              minWidth: '100%',
              borderCollapse: 'collapse'
            }}>
              <thead style={{
                backgroundColor: darkMode ? '#4B5563' : '#F9FAFB',
                borderBottom: `1px solid ${darkMode ? '#6B7280' : '#E5E7EB'}`
              }}>
                <tr>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Document #
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Name
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Service
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Software
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Issue
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Type
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Size
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Groups
                  </th>
                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Uploaded By
                  </th>

                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'left',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Upload Date
                  </th>

                  <th style={{
                    padding: '0.75rem 1.5rem',
                    textAlign: 'right',
                    fontSize: '0.75rem',
                    fontWeight: '600',
                    color: darkMode ? '#D1D5DB' : '#6B7280',
                    textTransform: 'uppercase',
                    letterSpacing: '0.05em'
                  }}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody style={{
                backgroundColor: darkMode ? '#374151' : '#FFFFFF',
                borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
              }}>
                {currentPageDocuments.map((document) => (
                  <tr key={document.id} style={{
                    borderBottom: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
                  }}>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: darkMode ? '#F9FAFB' : '#111827'
                    }}>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        display: 'inline-flex',
                        fontSize: '0.75rem',
                        lineHeight: '1.25rem',
                        fontWeight: '600',
                        borderRadius: '9999px',
                        backgroundColor: darkMode ? '#05966930' : '#D1FAE5',
                        color: darkMode ? '#A7F3D0' : '#059669'
                      }}>
                        {document.document_number || 'N/A'}
                      </span>
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: darkMode ? '#F9FAFB' : '#111827',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '0.5rem',
                        overflow: 'hidden',
                        padding: '0.5rem 0',
                        maxWidth: '250px'
                      }}>
                        <div style={{
                          minWidth: '2rem',
                          height: '2rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '0.375rem',
                          backgroundColor: document.type === 'PDF' ? (darkMode ? '#DC262630' : '#FEE2E2') :
                                        document.type === 'DOCX' ? (darkMode ? '#2563EB30' : '#DBEAFE') :
                                        document.type === 'XLSX' ? (darkMode ? '#05966930' : '#D1FAE5') :
                                        (darkMode ? '#7C3AED30' : '#EDE9FE'),
                          flexShrink: 0,
                          marginTop: '0.125rem'
                        }}>
                          <DocumentTextIcon style={{
                            height: '1.25rem',
                            width: '1.25rem',
                            color: document.type === 'PDF' ? (darkMode ? '#FCA5A5' : '#DC2626') :
                                  document.type === 'DOCX' ? (darkMode ? '#93C5FD' : '#2563EB') :
                                  document.type === 'XLSX' ? (darkMode ? '#A7F3D0' : '#059669') :
                                  (darkMode ? '#C4B5FD' : '#7C3AED')
                          }} />
                        </div>
                        <div style={{
                          display: 'block',
                          width: '100%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }}>
                          <span style={{
                            display: 'block',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            lineHeight: '1.25rem'
                          }}>
                            {document.name || document.filename || 'Unnamed Document'}
                          </span>
                          <span style={{
                            display: 'block',
                            fontSize: '0.75rem',
                            color: darkMode ? '#9CA3AF' : '#6B7280',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}>
                            {document.filename && document.name !== document.filename ? document.filename : ''}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {document.service_name || '-'}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {document.software_menus || '-'}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {document.issue_type || '-'}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {document.type}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {document.size}
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.25rem' }}>
                        {document.groups && Array.isArray(document.groups) ? (
                          document.groups.map((group, index) => (
                            <span key={index} style={{
                              padding: '0.125rem 0.5rem',
                              display: 'inline-flex',
                              fontSize: '0.75rem',
                              lineHeight: '1.25rem',
                              fontWeight: '600',
                              borderRadius: '9999px',
                              backgroundColor: darkMode ? '#1d4ed830' : '#dbeafe',
                              color: darkMode ? '#bfdbfe' : '#1d4ed8'
                            }}>
                              {group}
                            </span>
                          ))
                        ) : (
                          <span style={{
                            padding: '0.125rem 0.5rem',
                            display: 'inline-flex',
                            fontSize: '0.75rem',
                            lineHeight: '1.25rem',
                            fontWeight: '600',
                            borderRadius: '9999px',
                            backgroundColor: darkMode ? '#4b546330' : '#f3f4f6',
                            color: darkMode ? '#9ca3af' : '#6b7280'
                          }}>
                            No groups
                          </span>
                        )}
                      </div>
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      <span style={{
                        padding: '0.125rem 0.5rem',
                        display: 'inline-flex',
                        fontSize: '0.75rem',
                        lineHeight: '1.25rem',
                        fontWeight: '600',
                        borderRadius: '9999px',
                        backgroundColor: darkMode ? '#7e22ce30' : '#f3e8ff',
                        color: darkMode ? '#e9d5ff' : '#7e22ce'
                      }}>
                        {document.uploaderName || 'Unknown'}
                      </span>
                    </td>
                    <td style={{
                      padding: '1rem 1.5rem',
                      whiteSpace: 'nowrap',
                      fontSize: '0.875rem',
                      color: darkMode ? '#D1D5DB' : '#6B7280'
                    }}>
                      {document.uploadDate}
                    </td>

                    <td style={{
                      padding: '1rem 1.5rem',
                      textAlign: 'right',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}>
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'flex-end',
                        height: '2rem',
                        gap: '0.5rem'
                      }}>
                        <button
                          onClick={async () => {
                            try {
                              setLoading(true);
                              const result = await getDocumentUrl(document.id);
                              if (!result.success) {
                                alert(`Failed to get document URL: ${result.error || 'Unknown error'}`);
                              } else {
                                // Open the document in a new tab
                                window.open(result.url, '_blank');
                              }
                            } catch (error) {
                              console.error('Error getting document URL:', error);
                              alert(`Error getting document URL: ${error.message || error}`);
                            } finally {
                              setLoading(false);
                            }
                          }}
                          style={{
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            color: darkMode ? '#60A5FA' : '#2563EB',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '2rem',
                            height: '2rem',
                            borderRadius: '0.375rem',
                            transition: 'background-color 0.2s ease'
                          }}
                          title="View Document"
                          onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? 'rgba(96, 165, 250, 0.1)' : 'rgba(37, 99, 235, 0.05)'}
                          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        >
                          <EyeIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                        </button>

                        <button
                          onClick={async () => {
                            if (window.confirm(`Are you sure you want to delete document "${document.name || document.filename}"? This action cannot be undone. This will remove the document from S3, the database, and all vector stores.`)) {
                              try {
                                setLoading(true);
                                const result = await deleteDocument(document.id);
                                if (!result.success) {
                                  alert(`Failed to delete document: ${result.error || 'Unknown error'}`);
                                } else {
                                  // Refresh documents list after deletion
                                  await fetchDocuments();

                                  // If we're on a page that would now be empty, go back to the previous page
                                  if (currentPageDocuments.length === 1 && currentDocPage > 1) {
                                    setCurrentDocPage(prev => prev - 1);
                                  }
                                }
                              } catch (error) {
                                console.error('Error deleting document:', error);
                                alert(`Error deleting document: ${error.message || error}`);
                              } finally {
                                setLoading(false);
                              }
                            }
                          }}
                          style={{
                            background: 'none',
                            border: 'none',
                            cursor: 'pointer',
                            color: darkMode ? '#F87171' : '#DC2626',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '2rem',
                            height: '2rem',
                            borderRadius: '0.375rem',
                            transition: 'background-color 0.2s ease'
                          }}
                          title="Delete Document"
                          onMouseOver={(e) => e.currentTarget.style.backgroundColor = darkMode ? 'rgba(248, 113, 113, 0.1)' : 'rgba(220, 38, 38, 0.05)'}
                          onMouseOut={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        >
                          <TrashIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Pagination Controls */}
            {filteredDocuments.length > documentsPerPage && (
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '1rem 1.5rem',
                borderTop: `1px solid ${darkMode ? '#4B5563' : '#E5E7EB'}`
              }}>
                <div style={{ fontSize: '0.875rem', color: darkMode ? '#D1D5DB' : '#6B7280' }}>
                  Showing {indexOfFirstDoc + 1}-{Math.min(indexOfLastDoc, filteredDocuments.length)} of {filteredDocuments.length} documents
                </div>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={handlePreviousDocPage}
                    disabled={currentDocPage === 1}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: currentDocPage === 1 ? (darkMode ? '#374151' : '#F3F4F6') : (darkMode ? '#4B5563' : '#FFFFFF'),
                      color: currentDocPage === 1 ? (darkMode ? '#6B7280' : '#9CA3AF') : (darkMode ? '#D1D5DB' : '#374151'),
                      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
                      cursor: currentDocPage === 1 ? 'not-allowed' : 'pointer'
                    }}
                    aria-label="Previous page"
                  >
                    <ChevronLeftIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                  </button>

                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.25rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: darkMode ? '#D1D5DB' : '#374151'
                  }}>
                    <span>Page {currentDocPage} of {totalDocPages}</span>
                  </div>

                  <button
                    onClick={handleNextDocPage}
                    disabled={currentDocPage === totalDocPages}
                    style={{
                      display: 'inline-flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: '0.5rem',
                      borderRadius: '0.375rem',
                      backgroundColor: currentDocPage === totalDocPages ? (darkMode ? '#374151' : '#F3F4F6') : (darkMode ? '#4B5563' : '#FFFFFF'),
                      color: currentDocPage === totalDocPages ? (darkMode ? '#6B7280' : '#9CA3AF') : (darkMode ? '#D1D5DB' : '#374151'),
                      border: `1px solid ${darkMode ? '#4B5563' : '#D1D5DB'}`,
                      cursor: currentDocPage === totalDocPages ? 'not-allowed' : 'pointer'
                    }}
                    aria-label="Next page"
                  >
                    <ChevronRightIcon style={{ height: '1.25rem', width: '1.25rem' }} />
                  </button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div style={{ padding: '1rem', textAlign: 'center' }}>
            <p style={{ color: darkMode ? '#D1D5DB' : '#6B7280' }}>No content to display</p>
          </div>
        )}
      </div>

      {/* Modals */}
      <UserModal
        isOpen={showUserModal}
        onClose={() => {
          setShowUserModal(false);
          setSelectedUser(null); // Clear selected user when closing modal
        }}
        editUser={selectedUser}
      />
      <GroupModal
        isOpen={showGroupModal}
        onClose={() => {
          setShowGroupModal(false);
          setSelectedGroup(null); // Clear selected group when closing modal
        }}
        editGroup={selectedGroup}
      />
      <DocumentModal isOpen={showDocumentModal} onClose={() => setShowDocumentModal(false)} />
    </div>
  );
};

export default UserManagement;
